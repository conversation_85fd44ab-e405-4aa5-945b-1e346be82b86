'use client'

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'

interface UserData {
  id: string
  name?: string | null
  email?: string | null
  image?: string | null
  admin?: boolean
  isDeveloper?: boolean
  createdAt?: string
  updatedAt?: string
}

interface UserContextType {
  user: UserData | null
  isLoading: boolean
  isAuthenticated: boolean
  refreshUser: () => Promise<UserData | null>
  updateUserData: (data: Partial<UserData>) => void
  lastRefresh: Date | null
}

const UserContext = createContext<UserContextType | undefined>(undefined)

interface UserProviderProps {
  children: React.ReactNode
}

export function UserProvider({ children }: UserProviderProps) {
  const { data: session, status, update } = useSession()
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)

  const user = session?.user || null
  const isLoading = status === 'loading' || isRefreshing
  const isAuthenticated = status === 'authenticated'

  // Function to refresh user data from the database
  const refreshUser = useCallback(async (): Promise<UserData | null> => {
    if (!session?.user?.id || isRefreshing) return null

    try {
      setIsRefreshing(true)

      const response = await fetch('/api/user/profile', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
        },
      })

      if (response.ok) {
        const userData = await response.json()

        // Update the session with fresh data
        await update({
          ...session,
          user: {
            ...session.user,
            name: userData.name,
            email: userData.email,
            image: userData.image,
            admin: userData.admin,
            isDeveloper: userData.isDeveloper,
          }
        })

        setLastRefresh(new Date())
        return userData
      } else {
        console.error('Failed to refresh user data:', response.status, response.statusText)
        return null
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error)
      return null
    } finally {
      setIsRefreshing(false)
    }
  }, [session, update, isRefreshing])

  // Function to update user data locally (optimistic updates)
  const updateUserData = useCallback((data: Partial<UserData>) => {
    if (session?.user) {
      update({
        ...session,
        user: {
          ...session.user,
          ...data
        }
      })
    }
  }, [session, update])

  // Auto-refresh user data periodically when authenticated
  useEffect(() => {
    if (isAuthenticated && session?.user?.id && !isRefreshing) {
      const shouldRefresh = !lastRefresh || Date.now() - lastRefresh.getTime() > 5 * 60 * 1000 // 5 minutes

      if (shouldRefresh) {
        refreshUser()
      }

      // Set up periodic refresh
      const interval = setInterval(() => {
        refreshUser()
      }, 5 * 60 * 1000) // 5 minutes

      return () => clearInterval(interval)
    }
  }, [isAuthenticated, session?.user?.id, lastRefresh, refreshUser, isRefreshing])

  const value: UserContextType = {
    user,
    isLoading,
    isAuthenticated,
    refreshUser,
    updateUserData,
    lastRefresh,
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser(): UserContextType {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
