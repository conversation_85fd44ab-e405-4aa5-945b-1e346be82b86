export interface FileTypeConfig {
  extensions: string[]
  mimeTypes: string[]
  magicNumbers: string[]
  maxSize: number // in bytes
  description: string
  category: 'archive' | 'executable' | 'mobile' | 'document' | 'image'
}

export const SUPPORTED_FILE_TYPES: Record<string, FileTypeConfig> = {
  // Archive files
  zip: {
    extensions: ['.zip'],
    mimeTypes: ['application/zip', 'application/x-zip-compressed'],
    magicNumbers: ['504B0304', '504B0506', '504B0708'],
    maxSize: 500 * 1024 * 1024, // 500MB
    description: 'ZIP Archive',
    category: 'archive'
  },
  rar: {
    extensions: ['.rar'],
    mimeTypes: ['application/vnd.rar', 'application/x-rar-compressed'],
    magicNumbers: ['526172211A0700', '526172211A070100'],
    maxSize: 500 * 1024 * 1024, // 500MB
    description: 'RAR Archive',
    category: 'archive'
  },
  '7z': {
    extensions: ['.7z'],
    mimeTypes: ['application/x-7z-compressed'],
    magicNumbers: ['377ABCAF271C'],
    maxSize: 500 * 1024 * 1024, // 500MB
    description: '7-Zip Archive',
    category: 'archive'
  },
  
  // Executable files
  exe: {
    extensions: ['.exe'],
    mimeTypes: ['application/vnd.microsoft.portable-executable', 'application/x-msdownload'],
    magicNumbers: ['4D5A'],
    maxSize: 1024 * 1024 * 1024, // 1GB
    description: 'Windows Executable',
    category: 'executable'
  },
  msi: {
    extensions: ['.msi'],
    mimeTypes: ['application/x-msi', 'application/x-ole-storage'],
    magicNumbers: ['D0CF11E0A1B11AE1'],
    maxSize: 1024 * 1024 * 1024, // 1GB
    description: 'Windows Installer Package',
    category: 'executable'
  },
  dmg: {
    extensions: ['.dmg'],
    mimeTypes: ['application/x-apple-diskimage'],
    magicNumbers: ['7801730D626260'],
    maxSize: 2 * 1024 * 1024 * 1024, // 2GB
    description: 'macOS Disk Image',
    category: 'executable'
  },
  
  // Mobile app files
  apk: {
    extensions: ['.apk'],
    mimeTypes: ['application/vnd.android.package-archive'],
    magicNumbers: ['504B0304'], // APK is essentially a ZIP file
    maxSize: 500 * 1024 * 1024, // 500MB
    description: 'Android Package',
    category: 'mobile'
  },
  ipa: {
    extensions: ['.ipa'],
    mimeTypes: ['application/octet-stream'],
    magicNumbers: ['504B0304'], // IPA is also a ZIP file
    maxSize: 500 * 1024 * 1024, // 500MB
    description: 'iOS App Store Package',
    category: 'mobile'
  },
  
  // Additional formats
  deb: {
    extensions: ['.deb'],
    mimeTypes: ['application/vnd.debian.binary-package'],
    magicNumbers: ['213C617263683E'],
    maxSize: 500 * 1024 * 1024, // 500MB
    description: 'Debian Package',
    category: 'executable'
  },
  rpm: {
    extensions: ['.rpm'],
    mimeTypes: ['application/x-rpm'],
    magicNumbers: ['EDABEEDB'],
    maxSize: 500 * 1024 * 1024, // 500MB
    description: 'Red Hat Package Manager',
    category: 'executable'
  },
  appimage: {
    extensions: ['.appimage'],
    mimeTypes: ['application/x-executable'],
    magicNumbers: ['7F454C46'], // ELF header
    maxSize: 1024 * 1024 * 1024, // 1GB
    description: 'AppImage',
    category: 'executable'
  }
}

export interface FileValidationResult {
  isValid: boolean
  fileType?: string
  detectedMimeType?: string
  detectedExtension?: string
  errors: string[]
  warnings: string[]
  size: number
  category?: string
}

export interface SecurityScanResult {
  isClean: boolean
  scanEngine: string
  scanDate: Date
  threats: string[]
  scanId?: string
  details?: any
}
