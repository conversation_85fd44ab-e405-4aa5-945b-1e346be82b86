/**
 * Utility functions for formatting file sizes, speeds, and time durations
 */

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  // Use appropriate decimal places based on size
  const decimals = size >= 100 ? 0 : size >= 10 ? 1 : 2
  return `${size.toFixed(decimals)} ${units[unitIndex]}`
}

export function formatSpeed(bytesPerSecond: number): string {
  if (bytesPerSecond === 0) return '0 B/s'
  
  return `${formatFileSize(bytesPerSecond)}/s`
}

export function formatDuration(seconds: number): string {
  if (seconds < 1) return '< 1s'
  if (seconds < 60) return `${Math.round(seconds)}s`
  if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`
  }
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.round((seconds % 3600) / 60)
  return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`
}

export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`
}

export function formatUploadProgress(
  uploadedBytes: number,
  totalBytes: number,
  speed: number,
  timeRemaining: number
): {
  progress: string
  uploaded: string
  speed: string
  timeRemaining: string
  percentage: number
} {
  const percentage = totalBytes > 0 ? (uploadedBytes / totalBytes) * 100 : 0
  
  return {
    progress: formatPercentage(percentage),
    uploaded: `${formatFileSize(uploadedBytes)} / ${formatFileSize(totalBytes)}`,
    speed: formatSpeed(speed),
    timeRemaining: timeRemaining > 0 ? formatDuration(timeRemaining) : '--',
    percentage
  }
}

export function calculateUploadSpeed(uploadedBytes: number, startTime: number): number {
  const elapsed = (Date.now() - startTime) / 1000 // seconds
  return elapsed > 0 ? uploadedBytes / elapsed : 0
}

export function estimateTimeRemaining(uploadedBytes: number, totalBytes: number, speed: number): number {
  if (speed <= 0 || uploadedBytes >= totalBytes) return 0
  
  const remainingBytes = totalBytes - uploadedBytes
  return remainingBytes / speed
}

export function formatChunkInfo(currentChunk: number, totalChunks: number): string {
  return `${currentChunk} / ${totalChunks}`
}

export function formatUploadStats(stats: {
  uploadedBytes: number
  totalBytes: number
  speed: number
  timeRemaining: number
  currentChunk: number
  totalChunks: number
  progress: number
}) {
  return {
    ...formatUploadProgress(stats.uploadedBytes, stats.totalBytes, stats.speed, stats.timeRemaining),
    chunks: formatChunkInfo(stats.currentChunk, stats.totalChunks),
    progressBar: Math.min(stats.progress, 100)
  }
}
