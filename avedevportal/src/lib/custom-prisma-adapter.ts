import type { <PERSON><PERSON><PERSON>, <PERSON>pterUser, Adapter<PERSON>ccount, AdapterSession, VerificationToken } from "next-auth/adapters"
import { PrismaClient } from "@prisma/client"

/**
 * Custom Prisma adapter for NextAuth.js that handles the timestamp field issues
 * This adapter avoids querying the problematic createdAt/updatedAt fields on Account model
 */
export function CustomPrismaAdapter(prisma: PrismaClient): Adapter {
  return {
    async createUser(user: Omit<AdapterUser, "id">): Promise<AdapterUser> {
      const data = {
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        image: user.image,
      }
      
      const createdUser = await prisma.user.create({ data })
      return {
        id: createdUser.id,
        name: createdUser.name,
        email: createdUser.email!,
        emailVerified: createdUser.emailVerified,
        image: createdUser.image,
      }
    },

    async getUser(id: string): Promise<AdapterUser | null> {
      const user = await prisma.user.findUnique({ where: { id } })
      if (!user) return null

      return {
        id: user.id,
        name: user.name,
        email: user.email!,
        emailVerified: user.emailVerified,
        image: user.image,
      }
    },

    async getUserByEmail(email: string): Promise<AdapterUser | null> {
      const user = await prisma.user.findUnique({ where: { email } })
      if (!user) return null

      return {
        id: user.id,
        name: user.name,
        email: user.email!,
        emailVerified: user.emailVerified,
        image: user.image,
      }
    },

    async getUserByAccount({ providerAccountId, provider }: Pick<AdapterAccount, "provider" | "providerAccountId">): Promise<AdapterUser | null> {
      try {
        // Use a more targeted query that avoids the problematic fields
        const account = await prisma.account.findUnique({
          where: {
            provider_providerAccountId: {
              provider,
              providerAccountId,
            },
          },
          select: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                emailVerified: true,
                image: true,
              }
            }
          },
        })
        
        if (!account?.user) return null
        
        return {
          id: account.user.id,
          name: account.user.name,
          email: account.user.email!,
          emailVerified: account.user.emailVerified,
          image: account.user.image,
        }
      } catch (error) {
        console.error('Error in getUserByAccount:', error)
        return null
      }
    },

    async updateUser(user: Partial<AdapterUser> & Pick<AdapterUser, "id">): Promise<AdapterUser> {
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: {
          name: user.name,
          email: user.email,
          emailVerified: user.emailVerified,
          image: user.image,
        },
      })

      return {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email!,
        emailVerified: updatedUser.emailVerified,
        image: updatedUser.image,
      }
    },

    async deleteUser(userId: string): Promise<void> {
      await prisma.user.delete({ where: { id: userId } })
    },

    async linkAccount(account: AdapterAccount): Promise<void> {
      // Create account without timestamp fields to avoid the null issue
      const data = {
        userId: account.userId,
        type: account.type,
        provider: account.provider,
        providerAccountId: account.providerAccountId,
        refresh_token: account.refresh_token,
        access_token: account.access_token,
        expires_at: account.expires_at,
        token_type: account.token_type,
        scope: account.scope,
        id_token: account.id_token,
        session_state: account.session_state,
      }
      
      await prisma.account.create({ data })
    },

    async unlinkAccount({ providerAccountId, provider }: Pick<AdapterAccount, "provider" | "providerAccountId">): Promise<void> {
      await prisma.account.delete({
        where: {
          provider_providerAccountId: {
            provider,
            providerAccountId,
          },
        },
      })
    },

    async createSession({ sessionToken, userId, expires }: { sessionToken: string; userId: string; expires: Date }): Promise<AdapterSession> {
      const session = await prisma.session.create({
        data: {
          sessionToken,
          userId,
          expires,
        },
      })

      return {
        sessionToken: session.sessionToken,
        userId: session.userId,
        expires: session.expires,
      }
    },

    async getSessionAndUser(sessionToken: string): Promise<{ session: AdapterSession; user: AdapterUser } | null> {
      const userAndSession = await prisma.session.findUnique({
        where: { sessionToken },
        include: { user: true },
      })
      
      if (!userAndSession) return null
      
      const { user, ...session } = userAndSession
      
      return {
        session: {
          sessionToken: session.sessionToken,
          userId: session.userId,
          expires: session.expires,
        },
        user: {
          id: user.id,
          name: user.name,
          email: user.email!,
          emailVerified: user.emailVerified,
          image: user.image,
        },
      }
    },

    async updateSession({ sessionToken, ...data }: Partial<AdapterSession> & Pick<AdapterSession, "sessionToken">): Promise<AdapterSession | null | undefined> {
      const session = await prisma.session.update({
        where: { sessionToken },
        data,
      })

      return {
        sessionToken: session.sessionToken,
        userId: session.userId,
        expires: session.expires,
      }
    },

    async deleteSession(sessionToken: string): Promise<void> {
      await prisma.session.delete({ where: { sessionToken } })
    },

    async createVerificationToken({ identifier, expires, token }: VerificationToken): Promise<VerificationToken | null | undefined> {
      const verificationToken = await prisma.verificationToken.create({
        data: {
          identifier,
          expires,
          token,
        },
      })

      return {
        identifier: verificationToken.identifier,
        expires: verificationToken.expires,
        token: verificationToken.token,
      }
    },

    async useVerificationToken({ identifier, token }: { identifier: string; token: string }): Promise<VerificationToken | null> {
      try {
        const verificationToken = await prisma.verificationToken.delete({
          where: {
            identifier_token: {
              identifier,
              token,
            },
          },
        })
        
        return {
          identifier: verificationToken.identifier,
          expires: verificationToken.expires,
          token: verificationToken.token,
        }
      } catch (error) {
        return null
      }
    },
  }
}
