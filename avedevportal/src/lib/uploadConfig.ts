// Enhanced upload configuration for large files
export const UPLOAD_CONFIG = {
  // Chunk sizes based on file size for optimal performance
  CHUNK_SIZES: {
    SMALL: 1024 * 1024,      // 1MB for files < 50MB
    MEDIUM: 5 * 1024 * 1024,  // 5MB for files 50MB-500MB
    LARGE: 10 * 1024 * 1024   // 10MB for files > 500MB
  },
  
  // File size thresholds
  THRESHOLDS: {
    SMALL_FILE: 50 * 1024 * 1024,   // 50MB
    LARGE_FILE: 500 * 1024 * 1024   // 500MB
  },
  
  // Upload settings
  MAX_RETRIES: 3,
  PARALLEL_UPLOADS: 3, // Number of concurrent chunks
  TIMEOUT: 60000, // 60 seconds per chunk
  
  // Storage limits
  MAX_FILE_SIZE: 5 * 1024 * 1024 * 1024, // 5GB max file size
  
  // Storage optimizations
  B2_PART_SIZE_MIN: 5 * 1024 * 1024, // 5MB minimum part size for B2
  B2_MAX_PARTS: 10000 // B2 limit on number of parts
}

/**
 * Get optimal chunk size based on file size
 */
export function getOptimalChunkSize(fileSize: number): number {
  if (fileSize < UPLOAD_CONFIG.THRESHOLDS.SMALL_FILE) {
    return UPLOAD_CONFIG.CHUNK_SIZES.SMALL
  } else if (fileSize < UPLOAD_CONFIG.THRESHOLDS.LARGE_FILE) {
    return UPLOAD_CONFIG.CHUNK_SIZES.MEDIUM
  } else {
    return UPLOAD_CONFIG.CHUNK_SIZES.LARGE
  }
}

/**
 * Validate file size and calculate optimal upload strategy
 */
export function getUploadStrategy(fileSize: number) {
  const chunkSize = getOptimalChunkSize(fileSize)
  const totalChunks = Math.ceil(fileSize / chunkSize)
  
  // Ensure we don't exceed B2's part limit
  if (totalChunks > UPLOAD_CONFIG.B2_MAX_PARTS) {
    const adjustedChunkSize = Math.ceil(fileSize / UPLOAD_CONFIG.B2_MAX_PARTS)
    return {
      chunkSize: Math.max(adjustedChunkSize, UPLOAD_CONFIG.B2_PART_SIZE_MIN),
      totalChunks: Math.ceil(fileSize / Math.max(adjustedChunkSize, UPLOAD_CONFIG.B2_PART_SIZE_MIN)),
      strategy: 'adjusted' as const
    }
  }
  
  return {
    chunkSize,
    totalChunks,
    strategy: totalChunks > 100 ? 'large' as const : 'standard' as const
  }
}
