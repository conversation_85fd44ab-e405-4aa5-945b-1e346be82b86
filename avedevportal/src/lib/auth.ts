import { NextAuthOptions } from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import { CustomPrismaAdapter } from './custom-prisma-adapter'
import { prisma } from './prisma'

export const authOptions: NextAuthOptions = {
  adapter: CustomPrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user && token?.sub) {
        session.user.id = token.sub

        // Add custom fields from token if they exist
        if (token.admin !== undefined) session.user.admin = token.admin as boolean
        if (token.isDeveloper !== undefined) session.user.isDeveloper = token.isDeveloper as boolean
      }
      return session
    },
    jwt: async ({ user, token, trigger }) => {
      // On initial sign in, fetch user data from database
      if (user) {
        token.uid = user.id

        // Fetch additional user data only on initial login
        try {
          const dbUser = await prisma.user.findUnique({
            where: { id: user.id },
            select: {
              admin: true,
              isDeveloper: true,
            }
          })

          if (dbUser) {
            token.admin = dbUser.admin
            token.isDeveloper = dbUser.isDeveloper
          }
        } catch (error) {
          console.error('Error fetching user data in JWT callback:', error)
        }
      }

      return token
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/login',
  },
  debug: process.env.NODE_ENV === 'development',
}
