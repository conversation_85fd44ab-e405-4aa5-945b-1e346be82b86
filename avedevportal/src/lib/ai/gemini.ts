export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
}

export interface ChatResponse {
  message: string
  suggestions?: string[]
  error?: string
}

export interface DatabaseContext {
  tables: string[]
  userStats: {
    totalUsers: number
    totalApps: number
    pendingApps: number
    approvedApps: number
  }
  recentActivity: Array<{
    type: string
    description: string
    timestamp: Date
  }>
}

/**
 * Google Gemini AI service for Avena AI
 */
export class GeminiAIService {
  private apiKey: string
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta'
  private model = 'gemini-2.0-flash-exp'

  constructor() {
    this.apiKey = process.env.GOOGLE_GEMINI_API_KEY || ''
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return !!this.apiKey
  }

  /**
   * Generate AI response for chat
   */
  async generateResponse(
    messages: ChatMessage[],
    context: DatabaseContext,
    userId: string
  ): Promise<ChatResponse> {
    if (!this.isConfigured()) {
      return {
        message: 'AI service is not configured. Please check your API key.',
        error: 'Configuration error'
      }
    }

    try {
      // Prepare the system prompt with database context
      const systemPrompt = this.buildSystemPrompt(context)

      // Format messages for Gemini API
      const formattedMessages = this.formatMessagesForGemini(messages, systemPrompt)

      const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: formattedMessages,
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        })
      })

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.statusText}`)
      }

      const data = await response.json()

      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response generated')
      }

      const aiMessage = data.candidates[0].content.parts[0].text
      const suggestions = this.extractSuggestions(aiMessage)

      return {
        message: aiMessage,
        suggestions
      }

    } catch (error) {
      console.error('Gemini AI error:', error)
      return {
        message: 'I apologize, but I encountered an error while processing your request. Please try again.',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Build system prompt with database context
   */
  private buildSystemPrompt(context: DatabaseContext): string {
    return `You are Avena AI, an intelligent assistant for the AveHub Developer Portal. You help developers and administrators manage their applications and understand the platform.

PLATFORM CONTEXT:
- Total Users: ${context.userStats.totalUsers}
- Total Apps: ${context.userStats.totalApps}
- Pending Apps: ${context.userStats.pendingApps}
- Approved Apps: ${context.userStats.approvedApps}

DATABASE STRUCTURE:
The platform uses the following main tables: ${context.tables.join(', ')}

RECENT ACTIVITY:
${context.recentActivity.map(activity => `- ${activity.type}: ${activity.description}`).join('\n')}

CAPABILITIES:
- Help with app management and development
- Provide platform statistics and insights
- Assist with troubleshooting and best practices
- Guide users through platform features
- Answer questions about app approval process
- Provide development recommendations

RESPONSE FORMATTING GUIDELINES:
- Use **bold text** for important terms and headings
- Use *italic text* for emphasis
- Use \`inline code\` for technical terms, file names, and commands
- Use code blocks with language specification for code examples:
  \`\`\`javascript
  console.log('example code');
  \`\`\`
- Use bullet points (•) for lists and feature descriptions
- Use numbered lists for step-by-step instructions
- Use ## for section headings when appropriate
- Keep responses well-structured and easy to read

GUIDELINES:
- Be helpful, friendly, and professional
- Provide accurate information based on the platform data
- Suggest actionable next steps when appropriate
- If you don't know something, admit it and suggest alternatives
- Keep responses concise but informative
- Always use proper markdown formatting for better readability
- Include code examples when relevant

Remember: You are specifically designed for the AveHub platform and should focus on helping users with their developer portal needs. Always format your responses using markdown for the best user experience.`
  }

  /**
   * Format messages for Gemini API
   */
  private formatMessagesForGemini(messages: ChatMessage[], systemPrompt: string) {
    const contents = []

    // Add system prompt as the first user message
    contents.push({
      role: 'user',
      parts: [{ text: systemPrompt }]
    })

    // Add a model response acknowledging the system prompt
    contents.push({
      role: 'model',
      parts: [{ text: 'I understand. I am Avena AI, your assistant for the AveHub Developer Portal. How can I help you today?' }]
    })

    // Add conversation messages
    for (const message of messages) {
      contents.push({
        role: message.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: message.content }]
      })
    }

    return contents
  }

  /**
   * Extract suggestions from AI response
   */
  private extractSuggestions(message: string): string[] {
    const suggestions: string[] = []

    // Look for common suggestion patterns
    const suggestionPatterns = [
      /You might want to (.*?)(?:\.|$)/gi,
      /Consider (.*?)(?:\.|$)/gi,
      /Try (.*?)(?:\.|$)/gi,
      /I recommend (.*?)(?:\.|$)/gi
    ]

    for (const pattern of suggestionPatterns) {
      const matches = message.matchAll(pattern)
      for (const match of matches) {
        if (match[1] && match[1].length < 100) {
          suggestions.push(match[1].trim())
        }
      }
    }

    return suggestions.slice(0, 3) // Limit to 3 suggestions
  }

  /**
   * Generate app development suggestions
   */
  async generateAppSuggestions(appData: {
    name: string
    category: string
    description: string
    tags: string[]
  }): Promise<string[]> {
    try {
      const prompt = `Based on this app information:
Name: ${appData.name}
Category: ${appData.category}
Description: ${appData.description}
Tags: ${appData.tags.join(', ')}

Provide 3 specific suggestions to improve this app for better user engagement and approval chances. Focus on practical, actionable advice.`

      const response = await this.generateResponse([
        { role: 'user', content: prompt, timestamp: new Date() }
      ], {
        tables: [],
        userStats: { totalUsers: 0, totalApps: 0, pendingApps: 0, approvedApps: 0 },
        recentActivity: []
      }, 'system')

      return response.suggestions || []
    } catch (error) {
      console.error('Error generating app suggestions:', error)
      return []
    }
  }

  /**
   * Analyze platform trends
   */
  async analyzeTrends(context: DatabaseContext): Promise<string> {
    try {
      const prompt = `Analyze the current platform trends based on this data:
- Total Users: ${context.userStats.totalUsers}
- Total Apps: ${context.userStats.totalApps}
- Pending Apps: ${context.userStats.pendingApps}
- Approved Apps: ${context.userStats.approvedApps}

Recent Activity:
${context.recentActivity.map(a => `- ${a.type}: ${a.description}`).join('\n')}

Provide insights about platform health, user engagement, and recommendations for improvement.`

      const response = await this.generateResponse([
        { role: 'user', content: prompt, timestamp: new Date() }
      ], context, 'system')

      return response.message
    } catch (error) {
      console.error('Error analyzing trends:', error)
      return 'Unable to analyze trends at this time.'
    }
  }
}

// Export singleton instance
export const geminiAIService = new GeminiAIService()
