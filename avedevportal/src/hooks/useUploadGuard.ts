import { useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface UseUploadGuardOptions {
  isUploading: boolean
  message?: string
}

export function useUploadGuard({ 
  isUploading, 
  message = 'Upload in progress. Are you sure you want to leave? All progress will be lost.' 
}: UseUploadGuardOptions) {
  const router = useRouter()

  // Prevent browser navigation/refresh during upload
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isUploading) {
        event.preventDefault()
        event.returnValue = message
        return message
      }
    }

    if (isUploading) {
      window.addEventListener('beforeunload', handleBeforeUnload)
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [isUploading, message])

  // Create a guarded navigation function
  const guardedNavigate = useCallback((url: string) => {
    if (isUploading) {
      const confirmed = window.confirm(message)
      if (confirmed) {
        router.push(url)
      }
    } else {
      router.push(url)
    }
  }, [isUploading, message, router])

  return { guardedNavigate }
}
