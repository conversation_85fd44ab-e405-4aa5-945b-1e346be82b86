'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState, useCallback } from 'react'

interface UserData {
  id: string
  name?: string | null
  email?: string | null
  image?: string | null
  admin?: boolean
  isDeveloper?: boolean
  createdAt?: string
  updatedAt?: string
}

interface UpdateProfileData {
  name: string
  email: string
}

interface UseAuthReturn {
  user: UserData | null | undefined
  isLoading: boolean
  isAuthenticated: boolean
  isUnauthenticated: boolean
  refreshUser: () => Promise<UserData | null>
  updateProfile: (data: UpdateProfileData) => Promise<{ success: boolean; user?: UserData; error?: string }>
}

export function useAuth(requireAuth = false): UseAuthReturn {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Redirect to login if auth is required and user is not authenticated
  useEffect(() => {
    if (requireAuth && status === 'unauthenticated') {
      router.push('/login')
    }
  }, [requireAuth, status, router])

  // Function to refresh user data from the database
  const refreshUser = useCallback(async (): Promise<UserData | null> => {
    if (!session?.user?.id || isRefreshing) return null

    try {
      setIsRefreshing(true)

      // Fetch fresh user data from the API
      const response = await fetch('/api/user/profile', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
        },
      })

      if (response.ok) {
        const userData = await response.json()
        return userData
      } else {
        console.error('Failed to refresh user data:', response.status, response.statusText)
        return null
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error)
      return null
    } finally {
      setIsRefreshing(false)
    }
  }, [session?.user?.id, isRefreshing])

  // Function to update user profile
  const updateProfile = useCallback(async (data: UpdateProfileData): Promise<{ success: boolean; user?: UserData; error?: string }> => {
    if (!session?.user?.id) {
      return { success: false, error: 'Not authenticated' }
    }

    try {
      setIsRefreshing(true)

      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        return { success: true, user: result.user }
      } else {
        return { success: false, error: result.error || 'Failed to update profile' }
      }
    } catch (error) {
      console.error('Failed to update profile:', error)
      return { success: false, error: 'Network error' }
    } finally {
      setIsRefreshing(false)
    }
  }, [session?.user?.id])

  return {
    user: session?.user,
    isLoading: status === 'loading' || isRefreshing,
    isAuthenticated: status === 'authenticated',
    isUnauthenticated: status === 'unauthenticated',
    refreshUser,
    updateProfile,
  }
}
