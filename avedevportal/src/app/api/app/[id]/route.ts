import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET - Get app details
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const app = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      },
      include: {
        developer: {
          select: {
            name: true,
            email: true
          }
        },
        versions: {
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    })

    if (!app) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    return NextResponse.json(app)
  } catch (error) {
    console.error('Error fetching app:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - Update app
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const {
      name,
      description,
      shortDescription,
      version,
      category,
      tags,
      websiteUrl,
      supportEmail,
      minVersion,
      maxVersion,
      changelog
    } = body

    // Validate required fields
    if (!shortDescription || !description || !category || !version) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Check if app exists and belongs to user
    const existingApp = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      }
    })

    if (!existingApp) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    // Create new version entry if version changed
    let versionEntry = null
    if (existingApp.version !== version) {
      versionEntry = await prisma.appVersion.create({
        data: {
          appId: id,
          version: version,
          downloadUrl: existingApp.downloadUrl,
          fileSize: existingApp.fileSize,
          changelog: changelog || `Updated to version ${version}`
        }
      })
    }

    // Update app
    const updatedApp = await prisma.app.update({
      where: { id: id },
      data: {
        description,
        shortDescription,
        version,
        category,
        tags: Array.isArray(tags) ? tags : [],
        website: websiteUrl || null,
        supportEmail: supportEmail || null,
        minVersion: minVersion || null,
        maxVersion: maxVersion || null,
        changelog: changelog || null,
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      app: updatedApp,
      message: 'App updated successfully'
    })

  } catch (error) {
    console.error('Error updating app:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE - Delete app
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if app exists and belongs to user
    const existingApp = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      }
    })

    if (!existingApp) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    // Delete app
    await prisma.app.delete({
      where: { id: id }
    })

    return NextResponse.json({
      success: true,
      message: 'App deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting app:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
