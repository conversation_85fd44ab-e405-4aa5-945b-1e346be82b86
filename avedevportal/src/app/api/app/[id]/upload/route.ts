import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { validateFile } from '@/lib/fileValidation'
import { appNotificationService } from '@/lib/email'
import { storageService } from '@/lib/storage'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now()
  console.log(`[UPLOAD] Starting upload process`)

  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    console.log(`[STORAGE UPLOAD] Processing upload for app ID: ${id}`)

    // Check if storage is configured
    if (!storageService.isConfigured()) {
      return NextResponse.json({ error: 'Storage service not configured' }, { status: 500 })
    }

    // Check if app exists and belongs to user
    const existingApp = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      }
    })

    if (!existingApp) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    const formData = await request.formData()
    const appFile = formData.get('appFile') as File | null
    const iconFile = formData.get('iconFile') as File | null
    const screenshotFiles = formData.getAll('screenshots') as File[]

    const uploadResults: any = {}

    // Handle app file upload with storage service
    if (appFile && appFile.size > 0) {
      console.log(`[STORAGE UPLOAD] Processing app file: ${appFile.name} (${appFile.size} bytes)`)
      const fileStartTime = Date.now()

      // Validate file type and security
      console.log(`[STORAGE UPLOAD] Starting file validation...`)
      const validationResult = await validateFile(appFile)
      console.log(`[STORAGE UPLOAD] File validation completed in ${Date.now() - fileStartTime}ms`)

      if (!validationResult.isValid) {
        return NextResponse.json({
          error: 'File validation failed',
          details: validationResult.errors
        }, { status: 400 })
      }

      console.log(`[STORAGE UPLOAD] Converting file to buffer...`)
      const bufferStartTime = Date.now()
      const appBuffer = Buffer.from(await appFile.arrayBuffer())
      console.log(`[STORAGE UPLOAD] Buffer conversion completed in ${Date.now() - bufferStartTime}ms`)      // Upload to storage service
      console.log(`[STORAGE UPLOAD] Uploading to storage...`)
      const uploadStartTime = Date.now()

      const uploadResult = await storageService.uploadFile(
        appBuffer,
        appFile.name,
        appFile.type || 'application/octet-stream',
        id,
        validationResult.fileType || 'unknown'
      )

      console.log(`[STORAGE UPLOAD] Upload completed in ${Date.now() - uploadStartTime}ms`)

      if (!uploadResult.success) {
        return NextResponse.json({
          error: 'Failed to upload to storage',
          details: uploadResult.error
        }, { status: 500 })
      }

      uploadResults.downloadUrl = uploadResult.url
      uploadResults.fileSize = appFile.size
      uploadResults.fileType = validationResult.fileType
      uploadResults.originalFileName = appFile.name
      uploadResults.storageKey = uploadResult.key
    }

    // Handle icon upload to storage
    if (iconFile && iconFile.size > 0) {
      console.log(`[STORAGE UPLOAD] Processing icon file: ${iconFile.name}`)
      const iconBuffer = Buffer.from(await iconFile.arrayBuffer())

      const iconUploadResult = await storageService.uploadFile(
        iconBuffer,
        `icon_${iconFile.name}`,
        iconFile.type || 'image/png',
        id,
        'icon'
      )

      if (iconUploadResult.success) {
        uploadResults.iconUrl = iconUploadResult.url
        console.log(`[STORAGE UPLOAD] Icon uploaded successfully: ${iconUploadResult.url}`)
      } else {
        console.error(`[STORAGE UPLOAD] Icon upload failed: ${iconUploadResult.error}`)
      }
    }

    // Handle screenshots upload to storage
    if (screenshotFiles.length > 0) {
      console.log(`[STORAGE UPLOAD] Processing ${screenshotFiles.length} screenshot files`)
      const screenshotUrls: string[] = []

      for (let i = 0; i < screenshotFiles.length && i < 5; i++) {
        const screenshot = screenshotFiles[i]
        if (screenshot.size > 0) {
          const screenshotBuffer = Buffer.from(await screenshot.arrayBuffer())

          const screenshotUploadResult = await storageService.uploadFile(
            screenshotBuffer,
            `screenshot_${i + 1}_${screenshot.name}`,
            screenshot.type || 'image/png',
            id,
            'screenshot'
          )

          if (screenshotUploadResult.success) {
            screenshotUrls.push(screenshotUploadResult.url!)
            console.log(`[STORAGE UPLOAD] Screenshot ${i + 1} uploaded successfully`)
          } else {
            console.error(`[STORAGE UPLOAD] Screenshot ${i + 1} upload failed: ${screenshotUploadResult.error}`)
          }
        }
      }

      if (screenshotUrls.length > 0) {
        uploadResults.screenshots = screenshotUrls
      }
    }

    // Update app with new file URLs if any were uploaded
    if (Object.keys(uploadResults).length > 0) {
      const updateData: any = {}

      if (uploadResults.downloadUrl) {
        updateData.downloadUrl = uploadResults.downloadUrl
        updateData.fileSize = uploadResults.fileSize
        updateData.fileType = uploadResults.fileType
        updateData.originalFileName = uploadResults.originalFileName

        // Store storage key for future file management
        if (uploadResults.storageKey) {
          updateData.storageKey = uploadResults.storageKey
        }

        // Set pending expiry date (5 days from now) if app is still pending
        if (existingApp.status === 'PENDING') {
          updateData.pendingExpiry = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)
        }
      }

      if (uploadResults.iconUrl) {
        updateData.iconUrl = uploadResults.iconUrl
      }

      if (uploadResults.screenshots) {
        updateData.screenshots = uploadResults.screenshots
      }

      console.log(`[UPLOAD] Updating database...`)
      const dbStartTime = Date.now()
      await prisma.app.update({
        where: { id: id },
        data: updateData
      })
      console.log(`[UPLOAD] Database update completed in ${Date.now() - dbStartTime}ms`)

      // Send notification email if this is a new upload
      if (uploadResults.downloadUrl && existingApp.status === 'PENDING') {
        console.log(`[UPLOAD] Sending notification email...`)
        const emailStartTime = Date.now()
        const developer = await prisma.user.findUnique({
          where: { id: existingApp.developerId },
          select: { email: true, name: true }
        })

        if (developer?.email) {
          await appNotificationService.notifyAppCreated(
            existingApp.developerId,
            existingApp.id,
            existingApp.name,
            developer.email
          )
        }
        console.log(`[UPLOAD] Email notification completed in ${Date.now() - emailStartTime}ms`)
      }
    }

    const totalTime = Date.now() - startTime
    console.log(`[UPLOAD] Upload process completed successfully in ${totalTime}ms`)

    return NextResponse.json({
      success: true,
      uploadResults,
      message: 'Files uploaded successfully',
      processingTime: totalTime
    })

  } catch (error) {
    const totalTime = Date.now() - startTime
    console.error(`[UPLOAD] Upload process failed after ${totalTime}ms:`, error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}