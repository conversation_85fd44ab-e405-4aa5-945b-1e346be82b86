import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check API key for external access
    const authHeader = request.headers.get('authorization')
    const apiKeyHeader = request.headers.get('x-api-key')
    
    const apiKey = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : apiKeyHeader
    
    if (!apiKey || apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    const { id: appId } = await params

    // Find the app with comprehensive details for external use
    const app = await prisma.app.findUnique({
      where: { id: appId },
      include: {
        developer: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        comments: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10 // Limit to latest 10 comments for performance
        },
        versions: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 5 // Latest 5 versions
        },
        _count: {
          select: {
            comments: true,
            versions: true
          }
        }
      }
    })

    if (!app) {
      return NextResponse.json({ 
        error: 'App not found',
        code: 'APP_NOT_FOUND'
      }, { status: 404 })
    }

    // Only return approved apps for external API
    if (app.status !== 'APPROVED') {
      return NextResponse.json({ 
        error: 'App not available',
        code: 'APP_NOT_AVAILABLE'
      }, { status: 403 })
    }

    // Calculate average rating if ratings exist
    const ratings = app.comments.filter((comment: any) => comment.rating).map((comment: any) => comment.rating);
    const averageRating = ratings.length > 0 
      ? ratings.reduce((sum: number, rating: number) => sum + rating, 0) / ratings.length 
      : null;

    // Format response for external consumption
    const response = {
      id: app.id,
      name: app.name,
      description: app.description,
      shortDescription: app.shortDescription,
      version: app.version,
      category: app.category,
      tags: app.tags,
      iconUrl: app.iconUrl,
      screenshots: app.screenshots,
      downloadUrl: app.downloadUrl,
      website: app.website,
      supportEmail: app.supportEmail,
      minVersion: app.minVersion,
      maxVersion: app.maxVersion,
      fileSize: app.fileSize,
      downloads: app.downloads,
      status: app.status,
      featured: app.featured,
      createdAt: app.createdAt,
      updatedAt: app.updatedAt,
      developer: app.developer,
      stats: {
        totalComments: app._count.comments,
        totalVersions: app._count.versions,
        averageRating: averageRating ? Math.round(averageRating * 10) / 10 : null,
        totalRatings: ratings.length
      },
      latestComments: app.comments.map((comment: any) => ({
        id: comment.id,
        content: comment.content,
        rating: comment.rating,
        user: comment.user,
        createdAt: comment.createdAt
      })),
      versionHistory: app.versions.map((version: any) => ({
        id: version.id,
        version: version.version,
        changelog: version.changelog,
        createdAt: version.createdAt
      }))
    };

    // Add CORS headers for external API usage
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
    };

    return NextResponse.json({ 
      success: true,
      app: response,
      timestamp: new Date().toISOString()
    }, { headers })

  } catch (error) {
    console.error('Error fetching app info:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 })
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  })
}
