import { NextRequest, NextResponse } from 'next/server'
import { geminiAIService } from '@/lib/ai/gemini'
import { aiTrainingService } from '@/lib/ai/training'

// CORS headers for external API access
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
  'Access-Control-Max-Age': '86400',
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders,
  })
}

export async function POST(request: NextRequest) {
  try {
    // Check API key for external access
    const apiKey = request.headers.get('X-API-Key') || request.headers.get('Authorization')?.replace('Bearer ', '')
    
    if (!apiKey || apiKey !== process.env.AVENA_AI_API_KEY) {
      return NextResponse.json(
        { error: 'Invalid or missing API key' },
        { status: 401, headers: corsHeaders }
      )
    }

    const body = await request.json()
    const { message, context, website } = body

    // Validate input
    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400, headers: corsHeaders }
      )
    }

    // Rate limiting check (simple implementation)
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown'
    const rateLimitKey = `ai_external_${clientIP}`
    
    // In production, implement proper rate limiting with Redis or similar
    // For now, we'll just log the request
    console.log(`External AI request from ${clientIP} for website: ${website}`)

    // Get database context
    const databaseContext = await aiTrainingService.getDatabaseContext()

    // Create a simplified context for external use
    const externalContext = {
      ...databaseContext,
      userStats: {
        ...databaseContext.userStats,
        // Don't expose sensitive user counts to external APIs
        totalUsers: Math.floor(databaseContext.userStats.totalUsers / 10) * 10, // Round to nearest 10
      },
      recentActivity: [] // Don't expose recent activity to external APIs
    }

    // Add website context if provided
    let enhancedMessage = message
    if (website) {
      enhancedMessage = `[External query from ${website}] ${message}`
    }

    // Generate AI response with limited context
    const response = await geminiAIService.generateResponse(
      [
        {
          role: 'user',
          content: enhancedMessage,
          timestamp: new Date()
        }
      ],
      externalContext,
      'external'
    )

    // Filter response for external use
    const externalResponse = {
      message: response.message,
      suggestions: response.suggestions?.slice(0, 2), // Limit suggestions for external use
      timestamp: new Date().toISOString()
    }

    return NextResponse.json(
      {
        success: true,
        response: externalResponse,
        source: 'AveHub Avena AI'
      },
      { headers: corsHeaders }
    )

  } catch (error) {
    console.error('External AI API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Unable to process your request at this time'
      },
      { status: 500, headers: corsHeaders }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check API key
    const apiKey = request.headers.get('X-API-Key') || request.headers.get('Authorization')?.replace('Bearer ', '')
    
    if (!apiKey || apiKey !== process.env.AVENA_AI_API_KEY) {
      return NextResponse.json(
        { error: 'Invalid or missing API key' },
        { status: 401, headers: corsHeaders }
      )
    }

    // Get basic platform stats for external use
    const context = await aiTrainingService.getDatabaseContext()
    const engagement = await aiTrainingService.getUserEngagementMetrics()

    return NextResponse.json(
      {
        success: true,
        service: {
          name: 'Avena AI',
          version: '1.0.0',
          platform: 'AveHub Developer Portal',
          status: 'operational'
        },
        stats: {
          totalApps: context.userStats.totalApps,
          approvedApps: context.userStats.approvedApps,
          averageRating: Math.round(engagement.averageRating * 10) / 10,
          totalDownloads: engagement.totalDownloads
        },
        capabilities: [
          'App development guidance',
          'Platform information',
          'General developer support',
          'Best practices recommendations'
        ],
        usage: {
          rateLimit: '100 requests per hour',
          authentication: 'API key required',
          formats: ['JSON']
        }
      },
      { headers: corsHeaders }
    )

  } catch (error) {
    console.error('External AI API status error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error' 
      },
      { status: 500, headers: corsHeaders }
    )
  }
}
