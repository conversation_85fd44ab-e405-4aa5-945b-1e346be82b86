import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { AITrainingService } from '@/lib/ai/training'

const aiTrainingService = new AITrainingService()

export async function GET(_request: NextRequest) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get platform statistics
    const [context, engagement] = await Promise.all([
      aiTrainingService.getDatabaseContext(),
      aiTrainingService.getUserEngagementMetrics()
    ])

    const stats = {
      totalApps: context.userStats.totalApps,
      approvedApps: context.userStats.approvedApps,
      totalDownloads: engagement.totalDownloads,
      averageRating: engagement.averageRating
    }

    return NextResponse.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 })
  }
}
