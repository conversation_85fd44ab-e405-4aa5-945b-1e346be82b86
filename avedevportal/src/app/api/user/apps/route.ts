import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Check API key
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    const apiKey = authHeader.split(' ')[1]
    if (apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 })
    }

    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch user's apps
    const apps = await prisma.app.findMany({
      where: {
        developerId: session.user.id
      },
      select: {
        id: true,
        name: true,
        status: true,
        downloads: true,
        version: true,
        iconUrl: true,
        shortDescription: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    return NextResponse.json({ apps })
  } catch (error) {
    console.error('Error fetching user apps:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
