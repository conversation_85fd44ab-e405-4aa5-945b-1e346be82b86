// filepath: /workspaces/avedevportal/src/app/api/test-storage/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { storageService } from '@/lib/storage'

export async function GET(request: NextRequest) {
  try {
    // Test if storage is configured
    const isConfigured = storageService.isConfigured()
    
    // Get storage stats if configured
    let stats = null
    if (isConfigured) {
      stats = await storageService.getStorageStats()
    }
    
    return NextResponse.json({
      configured: isConfigured,
      service: 'Netlify Blobs',
      stats,
      config: {
        siteId: process.env.NETLIFY_SITE_ID,
        tokenLength: process.env.NETLIFY_TOKEN?.length || 0,
        maxFileSize: process.env.MAX_FILE_SIZE || '104857600',
        apiUrl: process.env.NETLIFY_API_URL || 'https://api.netlify.com/api/v1'
      }
    })
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
