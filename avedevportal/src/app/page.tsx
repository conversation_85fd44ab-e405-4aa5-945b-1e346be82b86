'use client'

import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { useAuth } from '@/hooks/useAuth'
import { useState, useEffect } from 'react'
import {
  Package,
  BarChart3,
  TrendingUp,
  Download,
  Star,
  Globe,
  ArrowUpRight,
  Plus,
  Eye,
  Code2,
  <PERSON><PERSON><PERSON>
} from 'lucide-react'

interface DashboardStats {
  totalApps: number
  totalDownloads: number
  averageRating: number
  approvedApps: number
}

interface RecentApp {
  id: string
  name: string
  status: string
  downloads: number
  iconUrl?: string
  shortDescription?: string
  createdAt: string
  updatedAt: string
}

export default function Dashboard() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentApps, setRecentApps] = useState<RecentApp[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // Fetch platform stats
      const statsResponse = await fetch('/api/dashboard/stats')
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData.stats)
      }

      // Fetch user's recent apps
      const appsResponse = await fetch('/api/user/apps', {
        headers: {
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_API_KEY}`
        }
      })
      if (appsResponse.ok) {
        const appsData = await appsResponse.json()
        setRecentApps(appsData.apps.slice(0, 3)) // Get only the 3 most recent
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  }

  const dashboardStats = [
    {
      name: 'Total Apps',
      value: loading ? '...' : formatNumber(stats?.totalApps || 0),
      change: '+2.1%',
      changeType: 'positive',
      icon: Package,
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'from-blue-500/10 to-cyan-500/10',
      borderColor: 'border-blue-500/20',
    },
    {
      name: 'Total Downloads',
      value: loading ? '...' : formatNumber(stats?.totalDownloads || 0),
      change: '+12.5%',
      changeType: 'positive',
      icon: Download,
      color: 'from-emerald-500 to-teal-500',
      bgColor: 'from-emerald-500/10 to-teal-500/10',
      borderColor: 'border-emerald-500/20',
    },
    {
      name: 'Approved Apps',
      value: loading ? '...' : formatNumber(stats?.approvedApps || 0),
      change: '+8.2%',
      changeType: 'positive',
      icon: Package,
      color: 'from-purple-500 to-indigo-500',
      bgColor: 'from-purple-500/10 to-indigo-500/10',
      borderColor: 'border-purple-500/20',
    },
    {
      name: 'Average Rating',
      value: loading ? '...' : (stats?.averageRating?.toFixed(1) || '0.0'),
      change: '+0.3',
      changeType: 'positive',
      icon: Star,
      color: 'from-amber-500 to-orange-500',
      bgColor: 'from-amber-500/10 to-orange-500/10',
      borderColor: 'border-amber-500/20',
    },
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-800/20 rounded-xl sm:rounded-2xl"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent rounded-xl sm:rounded-2xl"></div>
          <div className="relative glass rounded-xl sm:rounded-2xl p-6 sm:p-8 border border-zinc-800/50">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-4">
                  <Sparkles className="w-5 h-5 sm:w-6 sm:h-6 text-blue-400" />
                  <span className="text-xs sm:text-sm font-medium text-zinc-400 uppercase tracking-wider">Developer Portal</span>
                </div>
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white via-zinc-100 to-zinc-300 bg-clip-text text-transparent mb-3">
                  Welcome back, {user?.name}
                </h1>
                <p className="text-base sm:text-lg text-zinc-400 max-w-2xl">
                  Manage your applications, monitor performance, and scale your development workflow with our cutting-edge platform.
                </p>
              </div>
              <div className="hidden lg:block flex-shrink-0">
                <div className="w-24 h-24 xl:w-32 xl:h-32 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-blue-500/20">
                  <Code2 className="w-12 h-12 xl:w-16 xl:h-16 text-blue-400" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {dashboardStats.map((stat) => (
            <div
              key={stat.name}
              className={`group relative overflow-hidden rounded-xl sm:rounded-2xl bg-gradient-to-br ${stat.bgColor} border ${stat.borderColor} hover-lift transition-all duration-300 hover:scale-105 touch-manipulation`}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
              <div className="relative p-4 sm:p-6">
                <div className="flex items-center justify-between mb-3 sm:mb-4">
                  <div className={`p-2 sm:p-3 rounded-lg sm:rounded-xl bg-gradient-to-br ${stat.color} shadow-lg`}>
                    <stat.icon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <ArrowUpRight className="w-4 h-4 sm:w-5 sm:h-5 text-zinc-500 group-hover:text-white transition-colors" />
                </div>
                <div className="space-y-2">
                  <p className="text-xs sm:text-sm font-medium text-zinc-400 uppercase tracking-wider">
                    {stat.name}
                  </p>
                  <p className="text-2xl sm:text-3xl font-bold text-white">
                    {stat.value}
                  </p>
                  <div className="flex items-center space-x-2">
                    <div className={`flex items-center space-x-1 px-2 py-1 rounded-full bg-gradient-to-r ${stat.color} bg-opacity-20`}>
                      <TrendingUp className="w-3 h-3 text-emerald-400" />
                      <span className="text-xs font-medium text-emerald-400">
                        {stat.change}
                      </span>
                    </div>
                    <span className="text-xs text-zinc-500 hidden sm:inline">vs last month</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Advanced Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Recent Applications */}
          <div className="lg:col-span-2 glass rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-zinc-800/50 hover-lift">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg">
                  <Package className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-white">Recent Applications</h3>
              </div>
              <button className="text-zinc-400 hover:text-white transition-colors p-1">
                <Eye className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
            </div>
            <div className="space-y-4">
              {loading ? (
                // Loading skeleton
                Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="group flex items-center space-x-4 p-4 rounded-xl bg-zinc-900/30 border border-zinc-800/50">
                    <div className="w-12 h-12 bg-zinc-800 rounded-xl animate-pulse"></div>
                    <div className="flex-1 min-w-0">
                      <div className="h-4 bg-zinc-800 rounded animate-pulse mb-2"></div>
                      <div className="h-3 bg-zinc-800 rounded animate-pulse w-2/3"></div>
                    </div>
                  </div>
                ))
              ) : recentApps.length > 0 ? (
                recentApps.map((app) => {
                  const getStatusColor = (status: string) => {
                    switch (status.toUpperCase()) {
                      case 'APPROVED':
                        return 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30'
                      case 'PENDING':
                        return 'bg-amber-500/20 text-amber-400 border border-amber-500/30'
                      case 'REJECTED':
                        return 'bg-red-500/20 text-red-400 border border-red-500/30'
                      default:
                        return 'bg-zinc-500/20 text-zinc-400 border border-zinc-500/30'
                    }
                  }

                  const getGradientColor = (index: number) => {
                    const colors = [
                      'from-emerald-500 to-teal-500',
                      'from-blue-500 to-indigo-500',
                      'from-purple-500 to-pink-500'
                    ]
                    return colors[index % colors.length]
                  }

                  return (
                    <div key={app.id} className="group flex items-center space-x-4 p-4 rounded-xl bg-zinc-900/30 border border-zinc-800/50 hover:bg-zinc-900/50 hover:border-zinc-700/50 transition-all duration-200">
                      <div className={`w-12 h-12 bg-gradient-to-br ${getGradientColor(recentApps.indexOf(app))} rounded-xl flex items-center justify-center shadow-lg`}>
                        {app.iconUrl ? (
                          <img src={app.iconUrl} alt={app.name} className="w-8 h-8 rounded-lg" />
                        ) : (
                          <Package className="w-6 h-6 text-white" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="font-semibold text-white truncate">{app.name}</p>
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(app.status)}`}>
                            {app.status}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-sm text-zinc-400">{formatNumber(app.downloads)} downloads</span>
                          <span className="text-sm text-zinc-400">
                            {new Date(app.updatedAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <ArrowUpRight className="w-5 h-5 text-zinc-500 group-hover:text-white transition-colors" />
                    </div>
                  )
                })
              ) : (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-zinc-600 mx-auto mb-4" />
                  <p className="text-zinc-400">No applications yet</p>
                  <p className="text-sm text-zinc-500 mt-1">Create your first app to get started</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            {/* Create New App */}
            <div className="glass rounded-2xl p-6 border border-zinc-800/50 hover-lift">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg glow-blue">
                  <Plus className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Create New App</h3>
                <p className="text-sm text-zinc-400 mb-4">Start building your next application</p>
                <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                  Get Started
                </button>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="glass rounded-2xl p-6 border border-zinc-800/50 hover-lift">
              <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full flex items-center space-x-3 p-3 rounded-xl bg-zinc-900/50 border border-zinc-800/50 hover:bg-zinc-900/80 hover:border-zinc-700/50 text-white transition-all duration-200 group">
                  <BarChart3 className="w-5 h-5 text-zinc-400 group-hover:text-blue-400" />
                  <span className="font-medium">View Analytics</span>
                  <ArrowUpRight className="w-4 h-4 text-zinc-500 group-hover:text-white ml-auto" />
                </button>
                <button className="w-full flex items-center space-x-3 p-3 rounded-xl bg-zinc-900/50 border border-zinc-800/50 hover:bg-zinc-900/80 hover:border-zinc-700/50 text-white transition-all duration-200 group">
                  <Globe className="w-5 h-5 text-zinc-400 group-hover:text-emerald-400" />
                  <span className="font-medium">API Documentation</span>
                  <ArrowUpRight className="w-4 h-4 text-zinc-500 group-hover:text-white ml-auto" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
