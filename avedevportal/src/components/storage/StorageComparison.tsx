'use client'

import { useState } from 'react'
import { Check, X, Info, ExternalLink, ChevronDown, ChevronUp } from 'lucide-react'
import { STORAGE_SERVICES, getRecommendedService } from '@/lib/storageServices'

interface StorageRequirements {
  maxFileSize: number
  storageNeeded: number
  mediaProcessing: boolean
  costSensitive: boolean
}

export function StorageComparison() {
  const [requirements, setRequirements] = useState<StorageRequirements>({
    maxFileSize: 100 * 1024 * 1024, // 100MB
    storageNeeded: 10, // 10GB
    mediaProcessing: false,
    costSensitive: true
  })
  
  const [expandedService, setExpandedService] = useState<string | null>(null)
  
  const recommendation = getRecommendedService(requirements)
  const recommendedServices = Array.isArray(recommendation) ? recommendation : [recommendation]
  
  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-white">Storage Service Comparison</h1>
        <p className="text-zinc-400 max-w-2xl mx-auto">
          Compare storage solutions for large file uploads on Vercel. 
          Your current setup with Netlify Blobs is highlighted.
        </p>
      </div>

      {/* Requirements Input */}
      <div className="bg-zinc-900/50 border border-zinc-700 rounded-xl p-6 space-y-4">
        <h2 className="text-xl font-semibold text-white mb-4">Your Requirements</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-zinc-300 mb-2">
              Max File Size (MB)
            </label>
            <input
              type="number"
              value={requirements.maxFileSize / (1024 * 1024)}
              onChange={(e) => setRequirements(prev => ({
                ...prev,
                maxFileSize: Number(e.target.value) * 1024 * 1024
              }))}
              className="w-full bg-zinc-800 border border-zinc-700 rounded-lg px-3 py-2 text-white"
              min="1"
              max="5000"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-zinc-300 mb-2">
              Storage Needed (GB)
            </label>
            <input
              type="number"
              value={requirements.storageNeeded}
              onChange={(e) => setRequirements(prev => ({
                ...prev,
                storageNeeded: Number(e.target.value)
              }))}
              className="w-full bg-zinc-800 border border-zinc-700 rounded-lg px-3 py-2 text-white"
              min="1"
              max="1000"
            />
          </div>
          
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="mediaProcessing"
              checked={requirements.mediaProcessing}
              onChange={(e) => setRequirements(prev => ({
                ...prev,
                mediaProcessing: e.target.checked
              }))}
              className="w-4 h-4 text-blue-500 bg-zinc-800 border-zinc-700 rounded"
            />
            <label htmlFor="mediaProcessing" className="text-zinc-300">
              Need media processing features
            </label>
          </div>
          
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="costSensitive"
              checked={requirements.costSensitive}
              onChange={(e) => setRequirements(prev => ({
                ...prev,
                costSensitive: e.target.checked
              }))}
              className="w-4 h-4 text-blue-500 bg-zinc-800 border-zinc-700 rounded"
            />
            <label htmlFor="costSensitive" className="text-zinc-300">
              Cost is a primary concern
            </label>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      {recommendedServices.length > 0 && (
        <div className="bg-blue-900/20 border border-blue-700 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-blue-300 mb-2 flex items-center">
            <Info className="w-5 h-5 mr-2" />
            Recommended for Your Needs
          </h3>
          <div className="space-y-2">
            {recommendedServices.map((service: any) => (
              <p key={service?.name} className="text-blue-200">
                <strong>{service?.name}</strong> - {service?.bestFor.join(', ')}
              </p>
            ))}
          </div>
        </div>
      )}

      {/* Services Comparison Table */}
      <div className="bg-zinc-900/50 border border-zinc-700 rounded-xl overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-zinc-800/50">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-zinc-300">Service</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-zinc-300">Free Storage</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-zinc-300">Max File Size</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-zinc-300">No Credit Card</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-zinc-300">Pricing</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-zinc-300">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-zinc-700">
              {STORAGE_SERVICES.map((service: any, index: number) => {
                const isRecommended = recommendedServices.some((rec: any) => rec?.name === service.name)
                const isNetlifyBlobs = service.name === "Netlify Blobs"
                const isExpanded = expandedService === service.name
                
                return (
                  <tr key={service.name} className={`
                    ${isNetlifyBlobs ? 'bg-green-900/20 border-green-700' : ''}
                    ${isRecommended && !isNetlifyBlobs ? 'bg-blue-900/10' : ''}
                  `}>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <div>
                          <div className="font-medium text-white flex items-center">
                            {service.name}
                            {isNetlifyBlobs && (
                              <span className="ml-2 bg-green-700 text-green-100 text-xs px-2 py-1 rounded">
                                CURRENT
                              </span>
                            )}
                            {isRecommended && !isNetlifyBlobs && (
                              <span className="ml-2 bg-blue-700 text-blue-100 text-xs px-2 py-1 rounded">
                                RECOMMENDED
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-zinc-400">
                            {service.bestFor.slice(0, 2).join(', ')}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-center text-white font-medium">
                      {service.freeStorage}
                    </td>
                    <td className="px-6 py-4 text-center text-white">
                      {service.maxFileSize}
                    </td>
                    <td className="px-6 py-4 text-center">
                      {service.creditCardRequired ? (
                        <X className="w-5 h-5 text-red-400 mx-auto" />
                      ) : (
                        <Check className="w-5 h-5 text-green-400 mx-auto" />
                      )}
                    </td>
                    <td className="px-6 py-4 text-center text-sm text-zinc-300">
                      {service.pricing}
                    </td>
                    <td className="px-6 py-4 text-center">
                      <button
                        onClick={() => setExpandedService(isExpanded ? null : service.name)}
                        className="text-blue-400 hover:text-blue-300 flex items-center space-x-1 mx-auto"
                      >
                        <span className="text-sm">Details</span>
                        {isExpanded ? (
                          <ChevronUp className="w-4 h-4" />
                        ) : (
                          <ChevronDown className="w-4 h-4" />
                        )}
                      </button>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Expanded Service Details */}
      {expandedService && (
        <div className="bg-zinc-900/50 border border-zinc-700 rounded-xl p-6">
          {STORAGE_SERVICES
            .filter((service: any) => service.name === expandedService)
            .map((service: any) => (
              <div key={service.name} className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-white">{service.name}</h3>
                  <button
                    onClick={() => setExpandedService(null)}
                    className="text-zinc-400 hover:text-zinc-300"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-green-300 mb-2">Pros</h4>
                    <ul className="space-y-1">
                      {service.pros.map((pro: string, index: number) => (
                        <li key={index} className="text-zinc-300 text-sm flex items-start">
                          <Check className="w-4 h-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-red-300 mb-2">Cons</h4>
                    <ul className="space-y-1">
                      {service.cons.map((con: string, index: number) => (
                        <li key={index} className="text-zinc-300 text-sm flex items-start">
                          <X className="w-4 h-4 text-red-400 mr-2 mt-0.5 flex-shrink-0" />
                          {con}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-blue-300 mb-2">Setup Instructions</h4>
                  <pre className="bg-zinc-800 border border-zinc-700 rounded-lg p-4 text-sm text-zinc-300 overflow-x-auto whitespace-pre-wrap">
                    {service.setupInstructions}
                  </pre>
                </div>
              </div>
            ))
          }
        </div>
      )}

      {/* Current Status */}
      <div className="bg-green-900/20 border border-green-700 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-green-300 mb-2 flex items-center">
          <Check className="w-5 h-5 mr-2" />
          Your Current Setup Status
        </h3>
        <div className="text-green-200 space-y-2">
          <p>✅ <strong>Netlify Blobs</strong> is already configured and optimal for your needs</p>
          <p>✅ <strong>2GB free storage</strong> meets your storage requirements</p>
          <p>✅ <strong>100MB file size limit</strong> - optimized for web apps</p>
          <p>✅ <strong>Simplified upload system</strong> already implemented</p>
          <p>✅ <strong>No credit card required</strong> for free tier</p>
          <p>✅ <strong>Global CDN included</strong> for fast file delivery</p>
        </div>
      </div>
    </div>
  )
}
