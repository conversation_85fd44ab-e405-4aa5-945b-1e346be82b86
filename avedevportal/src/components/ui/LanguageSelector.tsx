'use client'

import React, { useState, useRef, useEffect } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useTranslations } from 'next-intl'
import { Globe, Check, ChevronDown } from 'lucide-react'
import { clsx } from 'clsx'

interface LanguageSelectorProps {
  className?: string
  variant?: 'default' | 'compact'
}

export function LanguageSelector({ className, variant = 'default' }: LanguageSelectorProps) {
  const { locale, setLocale, availableLanguages, isRTL } = useLanguage()
  const t = useTranslations('languages')
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  const currentLanguage = availableLanguages.find(lang => lang.code === locale)
  
  const filteredLanguages = availableLanguages.filter(lang =>
    lang.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lang.nativeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lang.code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isOpen])

  const handleLanguageSelect = (languageCode: string) => {
    setLocale(languageCode)
    setIsOpen(false)
    setSearchTerm('')
  }

  const toggleDropdown = () => {
    setIsOpen(!isOpen)
    setSearchTerm('')
  }

  if (variant === 'compact') {
    return (
      <div className={clsx('relative', className)} ref={dropdownRef}>
        <button
          onClick={toggleDropdown}
          className="flex items-center space-x-2 px-3 py-2 bg-zinc-800/50 hover:bg-zinc-700/50 border border-zinc-700/50 rounded-lg transition-colors"
          aria-label="Select language"
        >
          <Globe className="w-4 h-4 text-zinc-400" />
          <span className="text-sm text-white font-medium">
            {currentLanguage?.code.toUpperCase()}
          </span>
          <ChevronDown className={clsx(
            'w-4 h-4 text-zinc-400 transition-transform',
            isOpen && 'rotate-180'
          )} />
        </button>

        {isOpen && (
          <div className={clsx(
            'absolute top-full mt-2 w-80 bg-zinc-900 border border-zinc-800 rounded-xl shadow-2xl z-50',
            isRTL ? 'right-0' : 'left-0'
          )}>
            <div className="p-3 border-b border-zinc-800">
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search languages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              />
            </div>
            <div className="max-h-64 overflow-y-auto">
              {filteredLanguages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => handleLanguageSelect(language.code)}
                  className={clsx(
                    'w-full flex items-center justify-between px-4 py-3 hover:bg-zinc-800/50 transition-colors text-left',
                    locale === language.code && 'bg-blue-600/20 text-blue-400'
                  )}
                >
                  <div className="flex flex-col">
                    <span className="text-white font-medium">{language.nativeName}</span>
                    <span className="text-sm text-zinc-400">{language.name}</span>
                  </div>
                  {locale === language.code && (
                    <Check className="w-4 h-4 text-blue-400" />
                  )}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={clsx('space-y-4', className)}>
      <div className="flex items-center justify-between p-4 bg-zinc-900/30 rounded-xl">
        <div>
          <h3 className="font-medium text-white">Language</h3>
          <p className="text-sm text-zinc-400">Choose your preferred language</p>
        </div>
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={toggleDropdown}
            className="flex items-center space-x-3 px-4 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-colors min-w-[200px]"
          >
            <Globe className="w-4 h-4 text-zinc-400" />
            <span className="flex-1 text-left">
              {currentLanguage?.nativeName || 'Select Language'}
            </span>
            <ChevronDown className={clsx(
              'w-4 h-4 text-zinc-400 transition-transform',
              isOpen && 'rotate-180'
            )} />
          </button>

          {isOpen && (
            <div className={clsx(
              'absolute top-full mt-2 w-96 bg-zinc-900 border border-zinc-800 rounded-xl shadow-2xl z-50',
              isRTL ? 'right-0' : 'left-0'
            )}>
              <div className="p-4 border-b border-zinc-800">
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search languages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                />
              </div>
              <div className="max-h-80 overflow-y-auto">
                {filteredLanguages.length > 0 ? (
                  filteredLanguages.map((language) => (
                    <button
                      key={language.code}
                      onClick={() => handleLanguageSelect(language.code)}
                      className={clsx(
                        'w-full flex items-center justify-between px-4 py-3 hover:bg-zinc-800/50 transition-colors text-left',
                        locale === language.code && 'bg-blue-600/20 text-blue-400'
                      )}
                    >
                      <div className="flex flex-col">
                        <span className="text-white font-medium">{language.nativeName}</span>
                        <span className="text-sm text-zinc-400">{language.name}</span>
                      </div>
                      {locale === language.code && (
                        <Check className="w-4 h-4 text-blue-400" />
                      )}
                    </button>
                  ))
                ) : (
                  <div className="px-4 py-8 text-center text-zinc-400">
                    No languages found matching "{searchTerm}"
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}