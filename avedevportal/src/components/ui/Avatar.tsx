'use client'

import { useState } from 'react'
import { User } from 'lucide-react'
import { cn } from '@/lib/utils'

interface AvatarProps {
  src?: string | null
  alt?: string
  name?: string | null
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function Avatar({ src, alt, name, className, size = 'md' }: AvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-12 h-12 text-base'
  }

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-6 h-6'
  }

  // Generate initials from name
  const getInitials = (name: string | null | undefined): string => {
    if (!name) return 'U'
    
    const words = name.trim().split(' ')
    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase()
    }
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase()
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
  }

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  // Show image if src exists and no error occurred
  if (src && !imageError) {
    return (
      <div className={cn('relative', sizeClasses[size], className)}>
        <img
          src={src}
          alt={alt || name || 'User avatar'}
          className={cn(
            'rounded-full object-cover transition-opacity duration-200',
            sizeClasses[size],
            imageLoading ? 'opacity-0' : 'opacity-100'
          )}
          onError={handleImageError}
          onLoad={handleImageLoad}
        />
        {/* Loading placeholder */}
        {imageLoading && (
          <div className={cn(
            'absolute inset-0 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center',
            sizeClasses[size]
          )}>
            <User className={cn('text-gray-500 dark:text-gray-400', iconSizes[size])} />
          </div>
        )}
      </div>
    )
  }

  // Fallback: Show initials or default icon
  return (
    <div className={cn(
      'bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium',
      sizeClasses[size],
      className
    )}>
      {name ? getInitials(name) : <User className={iconSizes[size]} />}
    </div>
  )
}
