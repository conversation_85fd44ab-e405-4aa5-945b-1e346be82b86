'use client'

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import { useEffect, useState } from 'react'
import { Copy, Check } from 'lucide-react'
import 'highlight.js/styles/github-dark.css'
import '@/styles/markdown.css'

interface MarkdownRendererProps {
  content: string
  className?: string
}

function CodeBlockCopyButton({ code }: { code: string }) {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy code:', error)
    }
  }

  return (
    <button
      onClick={handleCopy}
      className="absolute top-2 right-2 p-2 bg-zinc-700/80 hover:bg-zinc-600/80 text-zinc-300 hover:text-white rounded-md transition-all duration-200 opacity-0 group-hover:opacity-100"
      title="Copy code"
    >
      {copied ? (
        <Check className="w-4 h-4 text-green-400" />
      ) : (
        <Copy className="w-4 h-4" />
      )}
    </button>
  )
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  useEffect(() => {
    // Import highlight.js languages dynamically
    const loadLanguages = async () => {
      try {
        const hljs = (await import('highlight.js')).default

        // Load common languages
        const languages = [
          { name: 'javascript', module: () => import('highlight.js/lib/languages/javascript') },
          { name: 'typescript', module: () => import('highlight.js/lib/languages/typescript') },
          { name: 'python', module: () => import('highlight.js/lib/languages/python') },
          { name: 'bash', module: () => import('highlight.js/lib/languages/bash') },
          { name: 'json', module: () => import('highlight.js/lib/languages/json') },
          { name: 'xml', module: () => import('highlight.js/lib/languages/xml') },
          { name: 'css', module: () => import('highlight.js/lib/languages/css') },
          { name: 'sql', module: () => import('highlight.js/lib/languages/sql') },
        ]

        for (const lang of languages) {
          try {
            const langModule = await lang.module()
            hljs.registerLanguage(lang.name, langModule.default)

            // Register HTML as XML
            if (lang.name === 'xml') {
              hljs.registerLanguage('html', langModule.default)
            }
          } catch (error) {
            console.warn(`Failed to load language ${lang.name}:`, error)
          }
        }
      } catch (error) {
        console.warn('Failed to load highlight.js:', error)
      }
    }

    loadLanguages()
  }, [])

  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // Headings
          h1: ({ children }) => (
            <h1 className="text-2xl font-bold text-white mb-4 border-b border-zinc-700 pb-2">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-xl font-bold text-white mb-3 mt-6">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-lg font-semibold text-white mb-2 mt-4">
              {children}
            </h3>
          ),
          h4: ({ children }) => (
            <h4 className="text-base font-semibold text-white mb-2 mt-3">
              {children}
            </h4>
          ),

          // Paragraphs
          p: ({ children }) => (
            <p className="text-zinc-100 mb-3 leading-relaxed">
              {children}
            </p>
          ),

          // Lists
          ul: ({ children }) => (
            <ul className="list-none space-y-2 mb-4 ml-0">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside space-y-2 mb-4 ml-4 text-zinc-100">
              {children}
            </ol>
          ),
          li: ({ children, ...props }) => (
            <li className="text-zinc-100 leading-relaxed" {...props}>
              {children}
            </li>
          ),

          // Emphasis and strong
          em: ({ children }) => (
            <em className="italic text-zinc-200">{children}</em>
          ),
          strong: ({ children }) => (
            <strong className="font-bold text-white">{children}</strong>
          ),

          // Inline code
          code: ({ children, className }) => {
            const isInline = !className

            if (isInline) {
              return (
                <code className="bg-zinc-800/80 text-blue-300 px-2 py-1 rounded-md text-sm font-mono border border-zinc-700/50">
                  {children}
                </code>
              )
            }

            // Extract language from className (format: language-xxx)
            const language = className?.replace('language-', '') || 'text'

            return (
              <code className={`${className} text-sm`}>
                {children}
              </code>
            )
          },

          // Code blocks
          pre: ({ children, ...props }) => {
            // Check if this is a code block with language
            const codeElement = children as any
            const language = codeElement?.props?.className?.replace('language-', '') || 'text'
            const codeContent = codeElement?.props?.children || ''

            return (
              <div className="relative mb-4 group">
                {/* Language label */}
                {language && language !== 'text' && (
                  <div className="absolute top-0 right-12 bg-zinc-700 text-zinc-300 px-2 py-1 text-xs rounded-bl-md rounded-tr-lg font-mono z-10">
                    {language}
                  </div>
                )}

                {/* Copy button */}
                <CodeBlockCopyButton code={String(codeContent)} />

                <pre className="bg-zinc-900/90 border border-zinc-700/50 rounded-lg p-4 overflow-x-auto font-mono text-sm leading-relaxed">
                  {children}
                </pre>
              </div>
            )
          },

          // Links
          a: ({ href, children }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 underline transition-colors"
            >
              {children}
            </a>
          ),

          // Blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-blue-500 pl-4 py-2 mb-4 bg-zinc-800/30 rounded-r-lg">
              <div className="text-zinc-200 italic">
                {children}
              </div>
            </blockquote>
          ),

          // Tables
          table: ({ children }) => (
            <div className="overflow-x-auto mb-4">
              <table className="min-w-full border border-zinc-700 rounded-lg">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-zinc-800">
              {children}
            </thead>
          ),
          tbody: ({ children }) => (
            <tbody className="bg-zinc-900/50">
              {children}
            </tbody>
          ),
          tr: ({ children }) => (
            <tr className="border-b border-zinc-700">
              {children}
            </tr>
          ),
          th: ({ children }) => (
            <th className="px-4 py-2 text-left text-white font-semibold">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-4 py-2 text-zinc-100">
              {children}
            </td>
          ),

          // Horizontal rule
          hr: () => (
            <hr className="border-zinc-700 my-6" />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}
