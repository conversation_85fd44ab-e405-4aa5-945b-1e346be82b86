'use client'

import { useState, useRef, useCallback } from 'react'
import { Upload, X, Check<PERSON>ircle, AlertCircle, Loader2, Pause, Play } from 'lucide-react'
import { useChunkedUpload, UploadProgress, UploadResult } from '@/hooks/useChunkedUpload'
import { getSupportedExtensions } from '@/lib/fileValidation'
import { formatFileSize, formatSpeed, formatDuration, formatUploadStats } from '@/lib/formatUtils'
import { getOptimalChunkSize } from '@/lib/uploadConfig'

interface EnhancedFileUploadProps {
  appId: string
  onUploadComplete?: (result: UploadResult) => void
  onUploadError?: (error: string) => void
  maxFileSize?: number
  acceptedTypes?: string[]
}

export function EnhancedFileUpload({
  appId,
  onUploadComplete,
  onUploadError,
  maxFileSize = 1024 * 1024 * 1024, // 1GB default
  acceptedTypes = getSupportedExtensions()
}: EnhancedFileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'completed' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [showConfirmCancel, setShowConfirmCancel] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  const { uploadFile, cancelUpload, isUploading, progress } = useChunkedUpload(appId, {
    chunkSize: selectedFile ? getOptimalChunkSize(selectedFile.size) : undefined,
    onProgress: (progressData: UploadProgress) => {
      // Progress is automatically tracked by the hook
    },
    onComplete: (result: UploadResult) => {
      if (result.success) {
        setUploadStatus('completed')
        onUploadComplete?.(result)
      } else {
        setUploadStatus('error')
        setErrorMessage(result.error || 'Upload failed')
        onUploadError?.(result.error || 'Upload failed')
      }
    },
    onError: (error: string) => {
      setUploadStatus('error')
      setErrorMessage(error)
      onUploadError?.(error)
    }
  })

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file size
    if (file.size > maxFileSize) {
      setErrorMessage(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxFileSize)})`)
      setUploadStatus('error')
      return
    }

    // Validate file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    if (!acceptedTypes.includes(fileExtension)) {
      setErrorMessage(`File type ${fileExtension} is not supported`)
      setUploadStatus('error')
      return
    }

    setSelectedFile(file)
    setUploadStatus('idle')
    setErrorMessage('')
  }, [maxFileSize, acceptedTypes])

  const handleUpload = useCallback(async () => {
    if (!selectedFile) return

    setUploadStatus('uploading')
    setErrorMessage('')

    try {
      await uploadFile(selectedFile)
    } catch (error) {
      console.error('Upload error:', error)
    }
  }, [selectedFile, uploadFile])

  const handleCancel = useCallback(() => {
    if (isUploading) {
      setShowConfirmCancel(true)
    } else {
      setSelectedFile(null)
      setUploadStatus('idle')
      setErrorMessage('')
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }, [isUploading])

  const confirmCancel = useCallback(async () => {
    await cancelUpload()
    setSelectedFile(null)
    setUploadStatus('idle')
    setErrorMessage('')
    setShowConfirmCancel(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [cancelUpload])

  // Format upload statistics using utility functions
  const uploadStats = formatUploadStats({
    uploadedBytes: progress.uploadedBytes,
    totalBytes: progress.totalBytes,
    speed: progress.speed,
    timeRemaining: progress.timeRemaining,
    currentChunk: progress.currentChunk,
    totalChunks: progress.totalChunks,
    progress: progress.progress
  })

  return (
    <div className="space-y-4">
      {/* File Selection */}
      <div className="border-2 border-dashed border-zinc-700 rounded-xl p-8 text-center hover:border-zinc-600 transition-colors">
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          accept={acceptedTypes.join(',')}
          className="hidden"
          disabled={isUploading}
        />

        {!selectedFile ? (
          <div className="space-y-4">
            <Upload className="w-12 h-12 text-zinc-500 mx-auto" />
            <div>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="text-blue-400 hover:text-blue-300 font-medium"
                disabled={isUploading}
              >
                Choose application file
              </button>
              <p className="text-zinc-500 text-sm mt-2">
                Supported formats: {acceptedTypes.slice(0, 5).join(', ')}
                {acceptedTypes.length > 5 && ` and ${acceptedTypes.length - 5} more`}
              </p>
              <p className="text-zinc-500 text-xs mt-1">
                Maximum file size: {formatFileSize(maxFileSize)}
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-center space-x-3">
              <div className="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center">
                <Upload className="w-5 h-5 text-blue-400" />
              </div>
              <div className="text-left">
                <p className="text-white font-medium">{selectedFile.name}</p>
                <p className="text-zinc-400 text-sm">{formatFileSize(selectedFile.size)}</p>
              </div>
            </div>

            {uploadStatus === 'idle' && (
              <div className="flex space-x-3 justify-center">
                <button
                  onClick={handleUpload}
                  className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  Start Upload
                </button>
                <button
                  onClick={handleCancel}
                  className="px-6 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg transition-colors"
                >
                  Remove
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <div className="bg-zinc-900/50 border border-zinc-700 rounded-xl p-6 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-white">Uploading...</h3>
            <button
              onClick={handleCancel}
              className="p-2 text-zinc-400 hover:text-red-400 transition-colors"
              title="Cancel upload"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-zinc-300">Progress</span>
              <span className="text-white font-medium">{uploadStats.progress}</span>
            </div>
            <div className="w-full bg-zinc-800 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-blue-600 to-blue-500 h-3 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${uploadStats.progressBar}%` }}
              />
            </div>
          </div>

          {/* Upload Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-zinc-400">Uploaded</p>
              <p className="text-white font-medium">{uploadStats.uploaded}</p>
            </div>
            <div>
              <p className="text-zinc-400">Speed</p>
              <p className="text-white font-medium">{uploadStats.speed}</p>
            </div>
            <div>
              <p className="text-zinc-400">Time Remaining</p>
              <p className="text-white font-medium">{uploadStats.timeRemaining}</p>
            </div>
            <div>
              <p className="text-zinc-400">Chunks</p>
              <p className="text-white font-medium">{uploadStats.chunks}</p>
            </div>
          </div>
        </div>
      )}

      {/* Upload Complete */}
      {uploadStatus === 'completed' && (
        <div className="bg-green-900/20 border border-green-700 rounded-xl p-6">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-6 h-6 text-green-400" />
            <div>
              <h3 className="text-lg font-semibold text-white">Upload Complete!</h3>
              <p className="text-green-300">Your application file has been uploaded successfully.</p>
            </div>
          </div>
        </div>
      )}

      {/* Upload Error */}
      {uploadStatus === 'error' && (
        <div className="bg-red-900/20 border border-red-700 rounded-xl p-6">
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-6 h-6 text-red-400" />
            <div>
              <h3 className="text-lg font-semibold text-white">Upload Failed</h3>
              <p className="text-red-300">{errorMessage}</p>
            </div>
          </div>
          <button
            onClick={() => {
              setUploadStatus('idle')
              setErrorMessage('')
            }}
            className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Cancel Confirmation Modal */}
      {showConfirmCancel && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-zinc-900 border border-zinc-700 rounded-xl p-6 max-w-md mx-4">
            <h3 className="text-lg font-semibold text-white mb-4">Cancel Upload?</h3>
            <p className="text-zinc-300 mb-6">
              Are you sure you want to cancel the upload? All progress will be lost.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={confirmCancel}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                Yes, Cancel
              </button>
              <button
                onClick={() => setShowConfirmCancel(false)}
                className="flex-1 px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg transition-colors"
              >
                Continue Upload
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
