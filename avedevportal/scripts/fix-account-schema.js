#!/usr/bin/env node

/**
 * Production-safe migration script to fix Account schema issues
 * This script adds missing timestamp fields to existing Account records
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixAccountSchema() {
  console.log('🚀 Starting Account schema fix...')
  
  try {
    // Check if we can connect to the database
    await prisma.$connect()
    console.log('✅ Connected to database')

    // Count existing accounts
    const totalAccounts = await prisma.account.count()
    console.log(`📊 Found ${totalAccounts} Account records`)
    
    if (totalAccounts === 0) {
      console.log('✅ No Account records found, no migration needed')
      return { success: true, updated: 0 }
    }

    // Get all accounts to check their current state
    const accounts = await prisma.account.findMany({
      select: {
        id: true,
        provider: true,
        providerAccountId: true,
        createdAt: true,
        updatedAt: true
      }
    })

    console.log(`🔍 Checking ${accounts.length} Account records for missing timestamps...`)

    const now = new Date()
    let updatedCount = 0
    let errors = []

    // Process each account
    for (const account of accounts) {
      try {
        // Check if the account needs timestamp updates
        const needsUpdate = !account.createdAt || !account.updatedAt

        if (needsUpdate) {
          await prisma.account.update({
            where: { id: account.id },
            data: {
              createdAt: account.createdAt || now,
              updatedAt: account.updatedAt || now
            }
          })
          updatedCount++
          
          if (updatedCount % 10 === 0) {
            console.log(`📝 Updated ${updatedCount} Account records...`)
          }
        }
      } catch (error) {
        console.error(`❌ Failed to update Account ${account.id}:`, error.message)
        errors.push({ accountId: account.id, error: error.message })
      }
    }

    console.log(`✅ Successfully updated ${updatedCount} Account records`)
    
    if (errors.length > 0) {
      console.warn(`⚠️  ${errors.length} errors occurred during migration:`)
      errors.forEach(({ accountId, error }) => {
        console.warn(`   - Account ${accountId}: ${error}`)
      })
    }

    // Verify the fix
    const accountsWithoutTimestamps = await prisma.account.findMany({
      where: {
        OR: [
          { createdAt: null },
          { updatedAt: null }
        ]
      },
      select: { id: true }
    })

    if (accountsWithoutTimestamps.length === 0) {
      console.log('🎉 All Account records now have proper timestamps!')
    } else {
      console.warn(`⚠️  ${accountsWithoutTimestamps.length} Account records still missing timestamps`)
    }

    return { 
      success: true, 
      updated: updatedCount, 
      errors: errors.length,
      remaining: accountsWithoutTimestamps.length 
    }

  } catch (error) {
    console.error('💥 Migration failed:', error)
    return { success: false, error: error.message }
  } finally {
    await prisma.$disconnect()
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  fixAccountSchema()
    .then((result) => {
      if (result.success) {
        console.log('🏁 Account schema fix completed successfully')
        console.log(`📊 Summary: Updated ${result.updated} records, ${result.errors || 0} errors`)
        process.exit(0)
      } else {
        console.error('💥 Account schema fix failed:', result.error)
        process.exit(1)
      }
    })
    .catch((error) => {
      console.error('💥 Unexpected error:', error)
      process.exit(1)
    })
}

module.exports = { fixAccountSchema }
