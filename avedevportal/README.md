# AVE Developer Portal API Documentation

## Overview

This document provides comprehensive documentation for all app-related APIs and endpoints in the AVE Developer Portal. The API allows you to manage applications, upload files, download apps, handle user operations, and integrate AI capabilities.

## Authentication

All API endpoints require authentication using an API key in the header:

```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

## Base URL

All endpoints are prefixed with your domain's base URL followed by `/api/`.

## Error Handling

The API returns standard HTTP status codes. Common error responses:

```json
{
  "error": "Error message description",
  "status": 400
}
```

## Rate Limiting

API requests are rate-limited to prevent abuse. If you exceed the rate limit, you'll receive a `429 Too Many Requests` response.

## CORS Configuration

The API is configured to accept requests from authorized origins. Ensure your domain is whitelisted for cross-origin requests.

---

## App Management Endpoints

### 1. Create App

**POST** `/api/app/create`

Creates a new application in the system.

**Headers:**
```http
Content-Type: application/json
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Request Body:**
```json
{
  "name": "My Application",
  "description": "Application description",
  "category": "productivity",
  "userId": "user_123"
}
```

**Response (201 Created):**
```json
{
  "id": "app_456",
  "name": "My Application",
  "description": "Application description",
  "category": "productivity",
  "userId": "user_123",
  "createdAt": "2024-01-15T10:30:00Z",
  "status": "active"
}
```

### 2. Get App Details

**GET** `/api/app/{id}`

Retrieves detailed information about a specific application.

**Headers:**
```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Response (200 OK):**
```json
{
  "id": "app_456",
  "name": "My Application",
  "description": "Application description",
  "category": "productivity",
  "userId": "user_123",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T11:00:00Z",
  "status": "active",
  "fileSize": 1024000,
  "downloadCount": 42
}
```

### 3. Update App

**PUT** `/api/app/{id}`

Updates an existing application's details.

**Headers:**
```http
Content-Type: application/json
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Request Body:**
```json
{
  "name": "Updated Application Name",
  "description": "Updated description",
  "category": "games"
}
```

**Response (200 OK):**
```json
{
  "id": "app_456",
  "name": "Updated Application Name",
  "description": "Updated description",
  "category": "games",
  "updatedAt": "2024-01-15T12:00:00Z"
}
```

### 4. Delete App

**DELETE** `/api/app/delete/{id}`

Permanently deletes an application and all associated files.

**Headers:**
```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Response (200 OK):**
```json
{
  "message": "Application deleted successfully",
  "deletedAppId": "app_456"
}
```

### 5. List Apps

**GET** `/api/app/list`

Retrieves a paginated list of all applications.

**Headers:**
```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `category` (optional): Filter by category
- `search` (optional): Search term

**Example Request:**
```http
GET /api/app/list?page=1&limit=10&category=productivity&search=calculator
```

**Response (200 OK):**
```json
{
  "apps": [
    {
      "id": "app_456",
      "name": "Calculator App",
      "description": "Simple calculator",
      "category": "productivity",
      "createdAt": "2024-01-15T10:30:00Z",
      "downloadCount": 42
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 50,
    "itemsPerPage": 10
  }
}
```

### 6. Get App Info

**GET** `/api/app/info/{id}`

Retrieves basic information about an application for public display.

**Headers:**
```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Response (200 OK):**
```json
{
  "id": "app_456",
  "name": "My Application",
  "description": "Application description",
  "category": "productivity",
  "downloadCount": 42,
  "rating": 4.5,
  "createdAt": "2024-01-15T10:30:00Z"
}
```

---

## File Management Endpoints

### 7. Upload App File

**POST** `/api/app/{id}/upload`

Uploads an application file (APK, IPA, etc.) to an existing app.

**Headers:**
```http
Content-Type: multipart/form-data
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Request Body (Form Data):**
```
file: [Binary file data]
version: "1.0.0" (optional)
releaseNotes: "Bug fixes and improvements" (optional)
```

**Response (200 OK):**
```json
{
  "message": "File uploaded successfully",
  "appId": "app_456",
  "fileName": "myapp_v1.0.0.apk",
  "fileSize": 1024000,
  "version": "1.0.0",
  "uploadedAt": "2024-01-15T14:30:00Z"
}
```

### 8. Upload App File (Chunked)

**POST** `/api/app/{id}/upload-chunk`

Uploads large application files in chunks for better reliability.

**Headers:**
```http
Content-Type: multipart/form-data
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Request Body (Form Data):**
```
chunk: [Binary chunk data]
chunkIndex: 0
totalChunks: 5
fileName: "myapp.apk"
fileHash: "sha256_hash_of_complete_file"
```

**Response (200 OK):**
```json
{
  "message": "Chunk uploaded successfully",
  "chunkIndex": 0,
  "totalChunks": 5,
  "isComplete": false
}
```

**Final Chunk Response:**
```json
{
  "message": "File upload completed",
  "appId": "app_456",
  "fileName": "myapp.apk",
  "fileSize": 5120000,
  "isComplete": true
}
```

### 9. Download App

**GET** `/api/app/download/{id}`

Downloads the application file.

**Headers:**
```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Query Parameters:**
- `version` (optional): Specific version to download

**Response (200 OK):**
- Content-Type: `application/octet-stream`
- Content-Disposition: `attachment; filename="myapp.apk"`
- Binary file data

---

## User Management Endpoints

### 10. Get User Apps

**GET** `/api/user/apps`

Retrieves all applications belonging to the authenticated user.

**Headers:**
```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Query Parameters:**
- `status` (optional): Filter by app status (active, draft, archived)

**Response (200 OK):**
```json
{
  "apps": [
    {
      "id": "app_456",
      "name": "My Application",
      "status": "active",
      "createdAt": "2024-01-15T10:30:00Z",
      "downloadCount": 42,
      "lastUpdated": "2024-01-15T12:00:00Z"
    }
  ],
  "totalCount": 3
}
```

### 11. Get User Profile

**GET** `/api/user/profile`

Retrieves the current user's profile information.

**Headers:**
```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Response (200 OK):**
```json
{
  "id": "user_123",
  "email": "<EMAIL>",
  "name": "John Developer",
  "createdAt": "2024-01-01T00:00:00Z",
  "totalApps": 3,
  "totalDownloads": 150,
  "accountType": "premium"
}
```

### 12. Update User Profile

**PUT** `/api/user/profile`

Updates the current user's profile information.

**Headers:**
```http
Content-Type: application/json
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Request Body:**
```json
{
  "name": "John Smith",
  "bio": "Mobile app developer",
  "website": "https://johnsmith.dev"
}
```

**Response (200 OK):**
```json
{
  "id": "user_123",
  "name": "John Smith",
  "bio": "Mobile app developer",
  "website": "https://johnsmith.dev",
  "updatedAt": "2024-01-15T15:00:00Z"
}
```

---

## Dashboard & Analytics Endpoints

### 13. Get Dashboard Stats

**GET** `/api/dashboard/stats`

Retrieves comprehensive statistics for the user's dashboard.

**Headers:**
```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Query Parameters:**
- `period` (optional): Time period for stats (7d, 30d, 90d, 1y)

**Response (200 OK):**
```json
{
  "totalApps": 5,
  "totalDownloads": 1250,
  "totalUsers": 89,
  "revenueGenerated": 450.00,
  "downloadTrends": [
    {
      "date": "2024-01-15",
      "downloads": 25
    }
  ],
  "topApps": [
    {
      "id": "app_456",
      "name": "My Application",
      "downloads": 500
    }
  ],
  "categoryStats": {
    "productivity": 3,
    "games": 2
  }
}
```

---

## AI Integration Endpoints

### 14. AI Chat

**POST** `/api/ai/chat`

Interacts with AI assistant for app development guidance.

**Headers:**
```http
Content-Type: application/json
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Request Body:**
```json
{
  "message": "How can I optimize my app for better performance?",
  "context": {
    "appId": "app_456",
    "platform": "android"
  }
}
```

**Response (200 OK):**
```json
{
  "response": "Here are some ways to optimize your Android app for better performance...",
  "suggestions": [
    "Optimize image sizes",
    "Use ProGuard for code minification",
    "Implement lazy loading"
  ],
  "conversationId": "conv_789"
}
```

### 15. External AI Integration

**POST** `/api/ai/external`

Integrates with external AI services for advanced features.

**Headers:**
```http
Content-Type: application/json
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

**Request Body:**
```json
{
  "service": "code-analysis",
  "payload": {
    "codeSnippet": "public class MainActivity extends AppCompatActivity { ... }",
    "language": "java"
  }
}
```

**Response (200 OK):**
```json
{
  "analysisResult": {
    "issues": [
      {
        "type": "performance",
        "message": "Consider using ViewBinding instead of findViewById",
        "line": 15
      }
    ],
    "score": 85,
    "recommendations": ["Use modern Android architecture components"]
  }
}
```

---

## Admin Endpoints

### 16. System Cleanup

**POST** `/api/admin/cleanup`

Performs system maintenance and cleanup operations (Admin only).

**Headers:**
```http
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
X-Admin-Token: admin_token_here
```

**Request Body:**
```json
{
  "operation": "cleanup-orphaned-files",
  "dryRun": false
}
```

**Response (200 OK):**
```json
{
  "message": "Cleanup completed successfully",
  "filesRemoved": 15,
  "spaceFreed": "2.5MB",
  "operationId": "cleanup_001"
}
```

---

## SDK Examples

### JavaScript/Node.js

```javascript
const axios = require('axios');

const apiClient = axios.create({
  baseURL: 'https://your-domain.com/api',
  headers: {
    'X-API-Key': 'AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI'
  }
});

// Create a new app
async function createApp(appData) {
  try {
    const response = await apiClient.post('/app/create', appData);
    return response.data;
  } catch (error) {
    console.error('Error creating app:', error.response.data);
  }
}

// Upload file
async function uploadFile(appId, fileBuffer, fileName) {
  const formData = new FormData();
  formData.append('file', fileBuffer, fileName);
  
  try {
    const response = await apiClient.post(`/app/${appId}/upload`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  } catch (error) {
    console.error('Error uploading file:', error.response.data);
  }
}
```

### Python

```python
import requests
import json

class AVEPortalAPI:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {'X-API-Key': api_key}
    
    def create_app(self, app_data):
        response = requests.post(
            f"{self.base_url}/app/create",
            json=app_data,
            headers=self.headers
        )
        return response.json()
    
    def upload_file(self, app_id, file_path):
        with open(file_path, 'rb') as file:
            files = {'file': file}
            response = requests.post(
                f"{self.base_url}/app/{app_id}/upload",
                files=files,
                headers=self.headers
            )
        return response.json()

# Usage
api = AVEPortalAPI('https://your-domain.com/api', 'AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI')
result = api.create_app({
    'name': 'My Python App',
    'description': 'Created via Python SDK',
    'category': 'productivity'
})
```

### cURL Examples

```bash
# Create an app
curl -X POST https://your-domain.com/api/app/create \
  -H "Content-Type: application/json" \
  -H "X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI" \
  -d '{
    "name": "Test App",
    "description": "Test application",
    "category": "productivity"
  }'

# Upload a file
curl -X POST https://your-domain.com/api/app/app_456/upload \
  -H "X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI" \
  -F "file=@./myapp.apk" \
  -F "version=1.0.0"

# Download an app
curl -X GET https://your-domain.com/api/app/download/app_456 \
  -H "X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI" \
  -o downloaded_app.apk
```

---

## Support

For additional support or questions about the API:

- **Documentation Issues**: Create an issue in the repository
- **API Support**: Contact <EMAIL>
- **Rate Limit Increases**: Contact your account manager

---

## Changelog

### v1.0.0
- Initial API release with full app management functionality
- File upload and download capabilities
- User management endpoints
- AI integration features
- Admin tools and analytics

---

*Last updated: January 2024*