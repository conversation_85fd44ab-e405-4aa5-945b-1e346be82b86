// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// NextAuth.js Models
model Account {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?  @db.String
  access_token      String?  @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?  @db.String
  session_state     String?
  createdAt         DateTime? @default(now())
  updatedAt         DateTime? @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  admin         Boolean   @default(false)
  isDeveloper   Boolean   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Email notification preferences
  emailNotifications Boolean @default(true)
  notifyAppUpdates   Boolean @default(true)
  notifyAppApproval  Boolean @default(true)
  notifyAppRejection Boolean @default(true)
  notifyAppSuspended Boolean @default(true)

  accounts      Account[]
  sessions      Session[]
  apps          App[]
  appComments   AppComment[]
  emailLogs     EmailLog[]
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Application Models
model App {
  id              String      @id @default(auto()) @map("_id") @db.ObjectId
  developerId     String      @db.ObjectId
  name            String
  description     String
  shortDescription String?
  version         String
  category        String
  tags            String[]    @default([])
  downloadUrl     String
  fileSize        Int         // in bytes
  screenshots     String[]    @default([])
  iconUrl         String?
  bannerUrl       String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  downloads       Int         @default(0)
  status          AppStatus   @default(PENDING)
  visibility      AppVisibility @default(PUBLIC)
  featured        Boolean     @default(false)
  minVersion      String?     // minimum system version required
  maxVersion      String?     // maximum system version supported
  website         String?
  supportEmail    String?
  changelog       String?

  // File management
  fileType        String?     // File extension/type
  originalFileName String?    // Original uploaded filename
  storageKey      String?     // Storage service key for file management
  virusScanStatus VirusScanStatus @default(PENDING)
  virusScanResult String?     // Scan result details
  tempStoragePath String?     // Temporary storage path for pending apps
  pendingExpiry   DateTime?   // When pending app expires (5 days from creation)

  developer       User        @relation(fields: [developerId], references: [id], onDelete: Cascade)
  comments        AppComment[]
  versions        AppVersion[]
  emailLogs       EmailLog[]

  @@index([status])
  @@index([category])
  @@index([featured])
  @@index([developerId])
  @@index([pendingExpiry])
}

model AppVersion {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  appId           String    @db.ObjectId
  version         String
  downloadUrl     String
  fileSize        Int
  changelog       String?
  createdAt       DateTime  @default(now())

  app             App       @relation(fields: [appId], references: [id], onDelete: Cascade)

  @@unique([appId, version])
}

model AppComment {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  appId           String    @db.ObjectId
  userId          String    @db.ObjectId
  content         String
  rating          Int?      // 1-5 star rating
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  app             App       @relation(fields: [appId], references: [id], onDelete: Cascade)
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([appId])
  @@index([userId])
}

// Email and notification models
model EmailLog {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  userId      String    @db.ObjectId
  appId       String?   @db.ObjectId
  emailType   EmailType
  recipient   String
  subject     String
  content     String
  status      EmailStatus @default(PENDING)
  sentAt      DateTime?
  errorMessage String?
  createdAt   DateTime  @default(now())

  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  app         App?      @relation(fields: [appId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([appId])
  @@index([emailType])
  @@index([status])
}

model EmailTemplate {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String    @unique
  subject     String
  htmlContent String
  textContent String?
  variables   String[]  @default([])
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

enum AppStatus {
  PENDING
  APPROVED
  REJECTED
  SUSPENDED
}

enum AppVisibility {
  PUBLIC
  PRIVATE
  UNLISTED
}

enum VirusScanStatus {
  PENDING
  SCANNING
  CLEAN
  INFECTED
  ERROR
  SKIPPED
}

enum EmailType {
  APP_CREATED
  APP_APPROVED
  APP_REJECTED
  APP_SUSPENDED
  APP_DELETED
  APP_UPDATED
  PENDING_EXPIRY_WARNING
  PENDING_EXPIRED
  SECURITY_ALERT
  WELCOME
}

enum EmailStatus {
  PENDING
  SENT
  FAILED
  BOUNCED
}
